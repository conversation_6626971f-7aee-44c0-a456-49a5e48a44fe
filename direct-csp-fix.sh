#!/bin/bash

# This script directly modifies the Nginx configuration to fix the CSP issue

echo "=== Direct CSP Fix ==="
echo "This script will directly modify your Nginx configuration to fix the CSP issue"

# Nginx configuration path
NGINX_CONFIG_PATH="/etc/nginx/sites-available/fone2go.talifouni.com"

# Check if the file exists
if [ ! -f "$NGINX_CONFIG_PATH" ]; then
    echo "Error: Nginx configuration file not found at $NGINX_CONFIG_PATH"
    echo "Please enter the correct path to your Nginx configuration file:"
    read -p "> " NGINX_CONFIG_PATH
    
    if [ ! -f "$NGINX_CONFIG_PATH" ]; then
        echo "Error: File not found. Exiting."
        exit 1
    fi
fi

# Backup the existing config
echo "Creating backup of current Nginx configuration..."
sudo cp "$NGINX_CONFIG_PATH" "${NGINX_CONFIG_PATH}.bak.$(date +%Y%m%d%H%M%S)"

# The new CSP header
NEW_CSP="add_header Content-Security-Policy \"default-src 'self'; connect-src 'self' https://*.supabase.co wss://*.supabase.co https://qvwbetihlprjphvyaawi.supabase.co wss://qvwbetihlprjphvyaawi.supabase.co https://fone2go.talifouni.com wss://fone2go.talifouni.com; script-src 'self' 'unsafe-inline' https://cdn.gpteng.co; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; font-src 'self' data:; frame-ancestors 'none';\" always;"

# Directly modify the file using sed
echo "Updating CSP header in Nginx configuration..."
sudo sed -i -E 's|add_header Content-Security-Policy.*always;|'"$NEW_CSP"'|g' "$NGINX_CONFIG_PATH"

# Test Nginx configuration
echo "Testing Nginx configuration..."
sudo nginx -t

if [ $? -eq 0 ]; then
    # Reload Nginx if the test was successful
    echo "Reloading Nginx..."
    sudo systemctl reload nginx
    echo "Nginx configuration updated and reloaded successfully."
    
    # Rebuild and restart the Docker container
    echo "Rebuilding and restarting the Docker container..."
    docker stop repair-qr-ninja || true
    docker rm repair-qr-ninja || true
    docker build -t repair-qr-ninja .
    docker run -d --name repair-qr-ninja -p 3001:3000 \
      -e NODE_ENV=production \
      -e VITE_SUPABASE_URL=${SUPABASE_URL:-https://qvwbetihlprjphvyaawi.supabase.co} \
      -e VITE_SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY:-eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF2d2JldGlobHByanBodnlhYXdpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ5NjQ0NzUsImV4cCI6MjA2MDU0MDQ3NX0.tjWg4fK22p50S2pIU898_BrsEL0rcc6p7HhK1Bzo4_U} \
      -v $(pwd)/public:/app/public \
      repair-qr-ninja
    
    echo ""
    echo "=== Fix Completed ==="
    echo "Please visit https://fone2go.talifouni.com/test-csp to test the CSP settings"
    echo "This page will show you the current CSP and allow you to test WebSocket connections"
else
    echo "Nginx configuration test failed. Reverting to backup..."
    sudo cp "${NGINX_CONFIG_PATH}.bak.$(ls -t ${NGINX_CONFIG_PATH}.bak.* | head -1)" "$NGINX_CONFIG_PATH"
    sudo systemctl reload nginx
    echo "Reverted to previous configuration."
    exit 1
fi
