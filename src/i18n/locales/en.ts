import { optional } from "zod";

const translations = {
  app: {
    title: "Fone2Go",
    slogan: "Welcome to Fone2Go",
  },
  common: {
    optional: "optional",
    dashboard: "Dashboard",
    newRepair: "New Repair",
    search: "Search",
    stock: "Stock & POS",
    searchRepairs: "Search Repairs",
    viewDetails: "View Details",
    clickToView: "Click to view",
    processing: "Processing...",
    submit: "Submit",
    cancel: "Cancel",
    delete: "Delete",
    optional: "optional",
    areYouSure: "Are you sure?",
    back: "Back to",
    loading: "Loading...",
    print: "Print",
    download: "Download",
    balance: "Balance",
    error: "Error!",
    pageNotFound: "Oops! Page not found",
    returnHome: "Return to Home",
    copied: "Copied to clipboard",
    call: "Call",
    startScanning: "Start Scanning",
    stopScanning: "Stop Scanning",
    scannerActive: "Scanner is active and ready",
    scannerInactive: "Scanner is inactive",
    logo: "Logo",
    repairShop: "Repair Shop",
    tel: "Tel",
    phone: "Phone",
    clear: "Clear",
    edit: "Edit",
    actions: "Actions",
    details: "Details",
    user: "User",
    date: "Date",
    premium: "Premium",
    viewDetails: "View Details",
    customerName: "Customer Name",
    customerPhone: "Customer Phone",
    notes: "Notes",
    all: "All",
  },
  repair: {
    createNew: "Create New Repair",
    customerInfo: "Customer Details",
    customerName: "Customer Name",
    customerPhone: "Phone Number",
    customerEmail: "Customer Email",
    phoneModel: "Device Model",
    stat: "Status",
    problemDescription: "Problem Description",
    observations: "Observations",
    observationsOptional: "(optional)",
    addObservation: "Add observation",
    noObservations: "No observations yet",
    observationAdded: "Observation added successfully",
    observationAddError: "Failed to add observation",
    customForm: "Custom Form",
    repairPrice: "Repair Price",
    initialPrice: "Initial Price",
    totalPrice: "Total Price",
    paymentStatus: "Payment Status",
    downPayment: "Down Payment",
    priceModifications: "Price Modifications",
    priceModified: "Price has been modified",
    addPriceModification: "Add Price Modification",
    priceModificationAdded: "Price modification added successfully",
    priceModificationAddError: "Failed to add price modification",
    amount: "Amount",
    reason: "Reason",
    createdAt: "Creation Date",
    submitRepair: "Submit Repair",
    repairDetails: "Repair Details",
    repairTimeline: "Repair Timeline",
    statusChanged: "Status changed to",
    updateStatus: "Update Status",
    deleteRepair: "Delete Repair",
    deleteConfirmation:
      "Are you sure you want to delete this repair? This action cannot be undone.",
    enterDeleteCode: "Enter deletion code",
    invalidDeleteCode: "Invalid deletion code. Please try again.",
    successCreated: "Repair created successfully!",
    failedCreated: "Failed to create repair!",
    successUpdated: "Repair updated successfully!",
    successDeleted: "Repair deleted successfully!",
    notFound: "Repair not found",
    active: "Active",
    completed: "Completed",
    returned: "Returned",
    all: "All",
    noActiveRepairs: "No active repairs found",
    noCompletedRepairs: "No completed repairs found",
    noReturnedRepairs: "No returned repairs found",
    noRepairsFound: "No repairs found",
    createFirstRepair: "Create your first repair",
    addedAgo: "Added {{time}}",
    scanQrCode: "Scan this QR code to view repair details",
    scanBarcode: "Scan this barcode with 1D scanner",
    barcodeSticker: "Barcode Sticker",
    repairTicket: "Repair Ticket",
    ticketNumber: "Ticket Number",
    qrCode: "QR Code",
    selectCodeType: "Select Code Type",
    selectCodeTypeDescription:
      "Choose which type of code to include on the printed ticket",
    repairNumber: "Repair Number",
    thankYou: "Thank you for your business!",
    bringReceipt: "Please bring this receipt when you pick up your device.",
    printTicket: "Print Ticket",
    status: {
      pending: "Pending",
      inProgress: "In Progress",
      completed: "Completed",
      returned: "Returned",
      cancelled: "Cancelled",
    },
    payment: {
      paid: "Paid",
      partial: "Partial",
      unpaid: "Unpaid",
    },
    addError: "Failed to add repair. Please try again.",
    fetchError: "Failed to fetch repairs. Please try again.",
    updateError: "Failed to update repair. Please try again.",
    deleteError: "Failed to delete repair. Please try again.",
    importError: "Failed to import repairs. Please try again.",
    noRepairFoundWithTicket:
      "No repair found with this ticket number. Please check and try again.",
    noTicketNumberForBarcode:
      "No ticket number available for barcode generation.",
    customFormCompleted: "Custom form completed",

    // Email notifications
    sendEmail: "Send Email",
    sendEmailToCustomer: "Send Email to Customer",
    emailSentSuccess: "Email sent successfully",
    emailSentError: "Failed to send email",
    invalidEmailAddress: "Invalid email address",
    emailTemplate: "Email Template",
    completionTemplate: "Repair Completion",
    customTemplate: "Custom Message",
    emailSubject: "Subject",
    emailMessage: "Message",
    enterEmailSubject: "Enter email subject",
    enterEmailMessage: "Enter your message to the customer",
    showPreview: "Show Preview",
    hidePreview: "Hide Preview",
    emailPreview: "Email Preview",
    to: "To",
    subject: "Subject",
    sending: "Sending...",
  },
  searchRepairs: {
    title: "Search Repairs",
    noResults: "No repairs found",
    adjustFilters: "Try adjusting your search or filters",
  },
  filters: {
    title: "Filters",
    slogan: "Filter repairs by status and payment",
    status: "Status",
    payment: "Payment Status",
    clear: "Clear filters",
    anyStatus: "Any status",
    anyPaymentStatus: "Any payment status",
    clearAll: "Clear all filters",
    dateFrom: "From Date",
    dateTo: "To Date",
  },
  importExport: {
    import: "Import",
    export: "Export",
    importRepairs: "Import Repairs",
    exportRepairs: "Export Repairs",
    chooseFile: "Choose File",
    downloadTemplate: "Download Template",
    importedSuccess: "Repairs imported successfully!",
    exportedSuccess: "Repairs exported successfully!",
    invalidFile: "Invalid file format. Please use a valid JSON file.",
    importConfirmation:
      "Are you sure you want to import these repairs? This will overwrite any existing data.",
  },
  analytics: {
    title: "Payment Analytics",
    totalAmountPaid: "Total Amount Paid",
    averageAmountPaid: "Average Amount Paid",
    monthlyRevenue: "Monthly Revenue",
  },
  auth: {
    login: "Login",
    logout: "Logout",
    email: "Email",
    password: "Password",
    loginToContinue: "Login to continue to your account",
    genericError: "An error occurred during login",
    emailRequired: "Email is required",
    passwordRequired: "Password is required",
    invalidCredentials: "Invalid email or password",
  },
  languageSwitcher: {
    language: "Language",
    english: "English",
    french: "French",
    arabic: "Arabic",
  },
  repairShop: {
    title: "Repair Shop",
    name: "Shop Name",
    address: "Address",
    phone: "Phone",
    fetchError: "Failed to fetch repair shop information",
  },
  scanner: {
    henexScanner: "Dedicated Scanner",
    cameraScanner: "Camera Scanner",
    barcode: "Barcode",
    henexInstructions:
      "Dedicated scanner is active. Scan a barcode or QR code directly.",
    scannerInstructions: "Press the scanner button to scan a code",
    scanPlaceholder: "Scan a code...",
    scanSuccess: "Code scanned successfully!",
    scanError: "Error scanning code. Please try again.",
    autoScanEnabled: "Auto-scanning enabled",
    readyToScan: "Ready to scan",
    scanningActive: "Scanner active",
  },
  shortcuts: {
    title: "Keyboard Shortcuts",
    description: "Use these keyboard shortcuts to navigate quickly",
    keyboard: "Shortcuts",
    showShortcuts: "Show keyboard shortcuts",
    tip: "Press ? anytime to show this help dialog",
    goToDashboard: "Go to Dashboard (Alt+H)",
    newRepair: "Create New Repair (Alt+N)",
    printCurrentRepair: "Print Current Repair (Alt+P)",
    help: "Show Keyboard Shortcuts (?)",
    navigatedToDashboard: "Navigated to Dashboard",
    creatingNewRepair: "Creating new repair",
    printing: "Printing...",
  },
  theme: {
    light: "Light",
    dark: "Dark",
    system: "System",
    toggleDarkMode: "Toggle dark mode",
  },
  dashboard: {
    title: "Dashboard",
    editLayout: "Edit Layout",
    saveLayout: "Save Layout",
    reset: "Reset",
    addWidget: "Add Widget",
    widgetAdded: "Widget added successfully",
    widgetRemoved: "Widget removed",
    dashboardReset: "Dashboard reset to default",
    widgetType: "Widget Type",
    widgetTitle: "Widget Title",
    selectWidgetType: "Select widget type",
    addWidgetDescription: "Choose a widget type and customize its title",
    totalRepairs: "Total Repairs",
    repairs: "Repairs",
    noRepairsFound: "No repairs found",
    unknownDevice: "Unknown Device",
    goodMorning: "Good Morning",
    goodAfternoon: "Good Afternoon",
    goodEvening: "Good Evening",
    user: "User",
    welcomeMessage:
      "Welcome to your personalized dashboard. Here you can see an overview of your repair shop.",
    widgets: {
      welcome: {
        title: "Welcome",
        defaultTitle: "Welcome",
      },
      "repair-status": {
        title: "Repair Status",
        defaultTitle: "Repair Status",
      },
      "recent-repairs": {
        title: "Recent Repairs",
        defaultTitle: "Recent Repairs",
      },
      "repairs-by-status": {
        title: "Repairs by Status",
        defaultTitle: "Repairs by Status",
      },
      "repairs-by-device": {
        title: "Repairs by Device",
        defaultTitle: "Repairs by Device",
      },
      "payment-status": {
        title: "Payment Status",
        defaultTitle: "Payment Status",
      },
      income: {
        title: "Income",
        defaultTitle: "Income",
      },
    },
    paid: "Paid",
    unpaid: "Unpaid",
    partial: "Partially Paid",
    noPaymentData: "No payment data available",
    monthlyIncome: "Monthly Income",
    totalIncome: "Total Income",
    income: "Income",
    increase: "increase",
    decrease: "decrease",
    allTime: "All time",
  },
  performanceMonitor: {
    title: "Performance Monitor",
    subtitle:
      "Monitor performance, analyze metrics, and optimize business operations",
    exportReport: "Export Performance Report",
    processOverview: "Process Overview",
    businessInsights: "Business Insights",
    improvementOpportunities: "Improvement Opportunities",
    analysisPeriod: "Analysis Period",
    analyzingWorkflow: "Analyzing performance patterns to optimize operations",
    workshopCapacity: "Workshop Capacity",
    totalProductiveTime: "Total productive time",
    processCompletion: "Process Completion",
    successfulOutcomes: "Successful outcomes",
    activeWorkflows: "Active Workflows",
    currentlyInProgress: "Currently in progress",
    processEfficiency: "Process Efficiency",
    averageCycleTime: "Average cycle time",
    overduePending: "Overdue Pending",
    pendingOverThreeDays: "Pending >3 days • Click to view",
    businessPerformanceMetrics: "Business Performance Metrics",
    operationalExcellence:
      "Key indicators for operational excellence and customer satisfaction",
    averageServiceTime: "Average Service Time",
    industryBenchmark: "Industry benchmark: 45-90min",
    processSuccessRate: "Process Success Rate",
    targetRate: "Target: >85%",
    throughput: "Throughput (repairs/hour)",
    workshopCapacityIndicator: "Workshop capacity indicator",
    strengthsIdentified: "Strengths Identified",
    optimizationOpportunities: "Optimization Opportunities",
    today: "Today",
    yesterday: "Yesterday",
    thisWeek: "This Week",
    thisMonth: "This Month",
  },
  workflowIntelligence: {
    title: "Workflow Intelligence",
    subtitle:
      "Optimize processes, improve efficiency, and enhance customer service",
    processOverview: "Process Overview",
    businessInsights: "Business Insights",
    improvementOpportunities: "Improvement Opportunities",
    analysisPeriod: "Analysis Period",
    analyzingWorkflow: "Analyzing workflow patterns to optimize operations",
    workshopCapacity: "Workshop Capacity",
    totalProductiveTime: "Total productive time",
    processCompletion: "Process Completion",
    successfulOutcomes: "Successful outcomes",
    activeWorkflows: "Active Workflows",
    currentlyInProgress: "Currently in progress",
    processEfficiency: "Process Efficiency",
    averageCycleTime: "Average cycle time",
    workshopResourceUtilization: "Workshop Resource Utilization",
    capacityDistribution: "Understanding capacity distribution across the team",
    processFlowAnalysis: "Process Flow Analysis",
    workflowStageDistribution:
      "Workflow stage distribution and bottleneck identification",
    businessPerformanceMetrics: "Business Performance Metrics",
    operationalExcellence:
      "Key indicators for operational excellence and customer satisfaction",
    averageServiceTime: "Average Service Time",
    industryBenchmark: "Industry benchmark: 45-90min",
    processSuccessRate: "Process Success Rate",
    targetRate: "Target: >85%",
    throughput: "Throughput (repairs/hour)",
    workshopCapacityIndicator: "Workshop capacity indicator",
    processImprovementOpportunities: "Process Improvement Opportunities",
    dataDrivernInsights:
      "Data-driven insights to enhance workshop efficiency and customer satisfaction",
    strengthsIdentified: "Strengths Identified",
    highCompletionRate: "High completion rate indicates good process control",
    consistentServiceTimes:
      "Consistent service times show standardized procedures",
    optimizationAreas: "Optimization Areas",
    workflowAutomation: "Consider workflow automation for routine tasks",
    peakHourAnalysis: "Analyze peak hours for better resource planning",
    exportIntelligenceReport: "Export Intelligence Report",
    accessDenied: "Access denied. You need workflow intelligence privileges.",
    today: "Today",
    yesterday: "Yesterday",
    thisWeek: "This Week",
    thisMonth: "This Month",
    inProgress: "In Progress",
    completed: "Completed",
    overduePending: "Overdue Pending",
    pendingOverThreeDays: "Pending >3 days • Click to view",
    performanceSummary: "Performance Summary",
    successRate: "Success Rate",
    avgServiceTime: "Avg Service Time",
    repairsPerHour: "Repairs/Hour",
    overdue: "Overdue",
    excellentSuccessRate: "Excellent Success Rate",
    successRateExceeds:
      "Your {{rate}}% completion rate exceeds industry standards",
    efficientServiceTimes: "Efficient Service Times",
    avgTimeOptimal:
      "Average {{time}} minutes per repair is within optimal range",
    highProductivity: "High Productivity",
    completingRepairsPerHour:
      "Completing {{rate}} repairs per hour shows strong efficiency",
    buildingFoundation: "Building Foundation",
    focusOnOptimization:
      "Focus on the optimization areas to improve performance metrics",
    optimizationOpportunities: "Optimization Opportunities",
    addressOverdueRepairs: "Address Overdue Repairs",
    repairsPendingAttention:
      "{{count}} repairs pending >3 days need immediate attention",
    improveCompletionRate: "Improve Completion Rate",
    currentRateCanImprove:
      "Current {{rate}}% rate can be improved to 85%+ target",
    optimizeServiceTime: "Optimize Service Time",
    avgTimeExceedsBenchmark: "Average {{time}} minutes exceeds 90min benchmark",
    increaseThroughput: "Increase Throughput",
    currentThroughputImprove: "Current {{rate}} repairs/hour can be improved",
    workflowStandardization: "Workflow Standardization",
    createChecklistsReduce:
      "Create checklists for common repair types to reduce variation",
    recommendedActions: "Recommended Actions",
    urgentProcessOverdue: "Urgent: Process Overdue Repairs",
    reviewPrioritizeRepairs:
      "Review and prioritize {{count}} repairs pending over 3 days",
    teamTraining: "Team Training",
    identifySkillGaps:
      "Identify skill gaps and provide targeted training for efficiency",
    processDocumentation: "Process Documentation",
    documentBestPractices:
      "Document best practices from high-performing periods",
    capacityPlanning: "Capacity Planning",
    useDataOptimizeStaffing:
      "Use data to optimize staffing during peak periods",
  },
  stock: {
    title: "Stock Management & POS",
    pos: "Point of Sale",
    products: "Products",
    categories: "Categories",
    sales: "Sales",
    inventory: "Inventory",
    reports: "Reports",

    // Product Management
    addProduct: "Add Product",
    editProduct: "Edit Product",
    deleteProduct: "Delete Product",
    productName: "Product Name",
    productDescription: "Description",
    sku: "SKU",
    barcode: "Barcode",
    category: "Category",
    price: "Price",
    cost: "Cost",
    stock: "Stock",
    minStock: "Min Stock",
    maxStock: "Max Stock",
    isActive: "Active",
    image: "Image",

    // Categories
    addCategory: "Add Category",
    editCategory: "Edit Category",
    categoryName: "Category Name",
    categoryDescription: "Category Description",

    // POS Interface
    cart: "Cart",
    addToCart: "Add to Cart",
    removeFromCart: "Remove",
    clearCart: "Clear Cart",
    quantity: "Quantity",
    unitPrice: "Unit Price",
    discount: "Discount",
    subtotal: "Subtotal",
    tax: "Tax",
    total: "Total",

    // Payment
    payment: "Payment",
    paymentMethod: "Payment Method",
    cash: "Cash",
    card: "Card",
    mobile: "Mobile Payment",
    mixed: "Mixed Payment",
    amountPaid: "Amount Paid",
    change: "Change",
    processSale: "Process Sale",

    // Customer
    customer: "Customer",
    customerName: "Customer Name",
    customerPhone: "Customer Phone",

    // Sales
    saleNumber: "Sale #",
    saleDate: "Sale Date",
    saleTotal: "Sale Total",
    viewSale: "View Sale",

    // Inventory
    lowStock: "Low Stock",
    outOfStock: "Out of Stock",
    stockLevel: "Stock Level",
    stockMovement: "Stock Movement",
    stockIn: "Stock In",
    stockOut: "Stock Out",
    adjustment: "Adjustment",

    // Messages
    productAdded: "Product added successfully",
    productUpdated: "Product updated successfully",
    productDeleted: "Product deleted successfully",
    categoryAdded: "Category added successfully",
    categoryUpdated: "Category updated successfully",
    categoryDeleted: "Category deleted successfully",
    saleCompleted: "Sale completed successfully",
    insufficientStock: "Insufficient stock",
    invalidQuantity: "Invalid quantity",
    fetchError: "Error fetching data",
    addError: "Error adding item",
    updateError: "Error updating item",
    deleteError: "Error deleting item",
    saleError: "Error processing sale",
    stockAdjusted: "Stock adjusted successfully",
    adjustError: "Error adjusting stock",

    // Touch Interface
    touchMode: "Touch Mode",
    gridView: "Grid View",
    listView: "List View",
    searchProducts: "Search products...",

    // Barcode Scanner
    scan: "Scan",
    scanBarcode: "Scan Barcode",
    barcodeScanner: "Barcode Scanner",
    manualEntry: "Manual Entry",
    cameraScanner: "Camera Scanner",
    enterBarcode: "Enter Barcode",
    productNotFound: "Product not found",
    productInactive: "Product is inactive",
    productOutOfStock: "Product is out of stock",
    barcodeAdded: "Product added to cart via barcode",
    scannerActive: "Scanner Active - Ready to scan",
    scanning: "Scanning",
    scannerInstructions: "Automatic barcode scanning is active:",
    useBarcodeScannerDevice: "Use barcode scanner",
    typeBarcodeDirect: "Type barcode directly",
    automaticCartAdd: "Auto-adds to cart",
    cameraStarted: "Camera started. Point at barcode to scan.",
    cameraError: "Could not access camera. Please use manual input.",
    enterBarcodeManually: "Enter Barcode Manually",
    enterOrScanBarcode: "Enter or scan barcode...",
    pointCameraAtBarcode: "Point camera at barcode to scan",
    orTypeBarcodeHere: "Or type barcode here...",
    cameraNotStarted: "Camera not started",
    startCamera: "Start Camera",

    // Sales Ticket & Printing
    salesTicket: "Sales Ticket",
    printTicket: "Print Ticket",
    print: "Print",
    thankYouVisit: "Thank you for your visit!",
    keepTicket: "Please keep this ticket for your records",
    printInstructions1: "Optimized for 80mm thermal printers",
    printInstructions2: "Ensure printer is connected and ready",
    printInstructions3: "Click Print to generate receipt",

    // Sales History
    todaysSales: "Today's Sales",
    todaysRevenue: "Today's Revenue",
    totalSales: "Total Sales",
    noSalesYet: "No sales yet",
    walkInCustomer: "Walk-in Customer",
    saleId: "Sale ID",
    totalValue: "Total Value",
    noLowStockItems: "No low stock items",
    noOutOfStockItems: "No out of stock items",

    // Product Management
    showInactive: "Show Inactive",
    hideInactive: "Hide Inactive",
    inactive: "Inactive",

    // Barcode Sticker
    generateSticker: "Generate Sticker",
    printSticker: "Print Sticker",
    generateBarcode: "Generate Barcode",
    generateEAN13Barcode: "Generate EAN13 Barcode",
    barcodeSticker: "Barcode Sticker",
    stickerPreview: "Sticker Preview",
    copies: "Copies",
    productNeedsBarcode: "This product needs a barcode",
    addBarcodeToProduct: "Add a barcode to this product to print stickers",
    downloadSticker: "Download Sticker",
    saveBarcodeToProduct: "Save Barcode to Product",
    barcodeGenerated: "Barcode generated successfully",
    barcodeSaved: "Barcode saved to product successfully",
    noBarcodeToSave: "No barcode to save",
    failedToGenerateBarcode: "Failed to generate barcode",
    failedToSaveBarcode: "Failed to save barcode to product",
    productHasBarcode: "Has Barcode",
    productNoBarcode: "No Barcode",
    generateNewBarcode:
      "This product doesn't have a barcode. Generate one below.",
    tryAdjustingSearch: "Try adjusting your search terms",
    getStartedAddProduct: "Get started by adding your first product",

    // POS Interface Messages
    cartIsEmpty: "Cart is empty",
    insufficientPayment: "Insufficient payment amount",
    saleFailed: "Sale failed",
    unknownError: "Unknown error",
    saleProcessedSuccessfully: "Sale processed successfully!",

    // Quick Setup
    userOrShopNotFound: "User or repair shop not found",
    errorCheckingSetup: "Error checking setup",
    errorCreatingSampleData: "Error creating sample data",
    sampleDataCreatedSuccessfully: "Sample data created successfully!",
    checking: "Checking...",
    checkSetup: "Check Setup",
    createSampleData: "Create Sample Data",

    // Product Form
    priceAndCostMustBePositive: "Price and cost must be positive numbers",
    enterProductName: "Enter product name",
    enterSKU: "Enter SKU",
    enterProductDescription: "Enter product description",

    // Stock Validation Messages
    productNoLongerExists: "Product no longer exists",
    productNoLongerActive: "Product is no longer active",
    insufficientStockDetailed:
      "Insufficient stock for {{productName}}. Available: {{available}}, Required: {{required}}",

    // Advanced POS Features
    holdOrder: "Hold Order",
    heldOrders: "Held Orders",
    recallOrder: "Recall Order",
    deleteOrder: "Delete Order",
    orderHeld: "Order held successfully",
    orderRecalled: "Order recalled successfully",
    orderDeleted: "Order deleted successfully",
    applyDiscount: "Apply Discount",
    orderDiscount: "Order Discount",
    itemDiscount: "Item Discount",
    discountType: "Discount Type",
    percentage: "Percentage",
    fixedAmount: "Fixed Amount",
    discountValue: "Discount Value",
    discountApplied: "Discount applied successfully",
    notes: "Notes",
    addNotes: "Add Notes",
    orderNotes: "Order Notes",
    noHeldOrders: "No held orders",
    heldOn: "Held on",
    orderTotal: "Order Total",
    discountAmount: "Discount Amount",
  },

  // Premium Features
  userManagement: {
    title: "User Management",
    createUser: "Create User",
    inviteUser: "Invite User",
    email: "Email",
    password: "Password",
    fullName: "Employee",
    role: "Role",
    administrator: "Administrator",
    technician: "Technician",
    receptionist: "Receptionist",
    cashier: "Cashier",
    userCreated: "User created successfully",
    userUpdated: "User updated successfully",
    userDeleted: "User deleted successfully",
    removeUser: "Remove User",
    changeRole: "Change Role",
    activityLog: "Activity Log",
    noUsers: "No users found for this shop",
    noActivity: "No activity logs found",
  },

  timeTracking: {
    title: "Time Tracking",
    overview: "Overview",
    entries: "Time Entries",
    reports: "Reports",
    totalTime: "Total Time",
    activeRepairs: "Active Repairs",
    activeTechnicians: "Active Technicians",
    completed: "Completed",
    activeNow: "Active Now",
    avgTime: "Avg Time",
    perRepair: "per repair",
    inProgress: "In progress",
    timeByTechnician: "Time by Technician",
    statusDistribution: "Status Distribution",
    technicianPerformance: "Technician Performance",
    timeDistribution: "Time Distribution",
    totalWorkTime: "Total Work Time",
    completionRate: "Completion Rate",
    avgRepairTime: "Avg Repair Time",
    perCompletedRepair: "Per completed repair",
    timeSpent: "Time Spent",
    repairs: "repairs",
    entries: "entries",
    repairsFinished: "repairs finished",
    noTimeEntries: "No time entries found for the selected filters",
    noPerformanceData: "No performance data available",
    noTimeData: "No time data available yet",
    period: "Period",
    today: "Today",
    yesterday: "Yesterday",
    thisWeek: "This Week",
    thisMonth: "This Month",
    allTechnicians: "All Technicians",
    editTimeEntry: "Edit Time Entry",
    duration: "Duration",
    durationMinutes: "Duration (minutes)",
    manualEditNotAvailable:
      "Manual editing not available - time is calculated from repair status changes",
    unknownUser: "Unknown User",
    started: "Started",
    repair: "Repair",
    status: "Status",
    actions: "Actions",
  },

  advancedStock: {
    title: "Advanced Stock Management",
    inventory: "Inventory",
    lowStock: "Low Stock",
    categories: "Categories",
    movements: "Stock Movements",
    addProduct: "Add Product",
    addCategory: "Add Category",
    recordMovement: "Record Movement",
    productInventory: "Product Inventory",
    lowStockAlerts: "Low Stock Alerts",
    productCategories: "Product Categories",
    stockMovements: "Stock Movements",
    noProducts: "No products found",
    noLowStock: "No low stock products found",
    noCategories: "No categories found",
    noMovements: "No stock movements found",
    restock: "Restock",
    editCategory: "Edit Category",
    editProduct: "Edit Product",
    adjust: "Adjust",
    stockIn: "Stock In",
    stockOut: "Stock Out",
    adjustment: "Direct Adjustment",
    reason: "Reason",
    reference: "Reference",
    newQuantity: "New Quantity",
    quantityToAddRemove: "Quantity to add/remove",
    whyChangeBeingMade: "Why is this change being made?",
    optionalReferenceNumber: "Optional reference number",
    stockAdded: "Stock added successfully",
    stockRemoved: "Stock removed successfully",
    stockAdjusted: "Stock adjusted successfully",
    notEnoughStock: "Not enough stock available",
    trackAllStockChanges: "Track all stock changes",
    dateTime: "Date & Time",
    product: "Product",
    type: "Type",
    quantity: "Quantity",
    user: "User",
    usedInRepair: "Used in repair",
  },

  assignTechnician: {
    assignTechnician: "Assign Technician",
    selectTechnician: "Select Technician",
    chooseTechnician: "Choose a technician",
    noAssignment: "No assignment",
    assignedTechnician: "Assigned Technician",
    technicianAssignmentUpdated: "Technician assignment updated",
    failedToUpdateTechnician: "Failed to update technician assignment",
  },

  customForms: {
    title: "Custom Forms",
    createForm: "Create Form",
    editForm: "Edit Form",
    deleteForm: "Delete Form",
    formName: "Form Name",
    formDescription: "Form Description",
    deviceType: "Device Type",
    fields: "Fields",
    addField: "Add Field",
    fieldName: "Field Name",
    fieldType: "Field Type",
    required: "Required",
    optional: "Optional",
    placeholder: "Placeholder",
    options: "Options",
    fieldOrder: "Field Order",
    textField: "Text Field",
    numberField: "Number Field",
    textareaField: "Textarea Field",
    selectField: "Select Field",
    checkboxField: "Checkbox Field",
    formCreated: "Form created successfully",
    formUpdated: "Form updated successfully",
    formDeleted: "Form deleted successfully",
    noForms: "No custom forms found",
    noFields: "No fields added yet",
    selectCustomForm: "Select Custom Form",
    noCustomForm: "No custom form",
    customFormCompleted: "Custom form completed",
    enterFieldName: "Enter field name",
    enterPlaceholder: "Enter placeholder text",
    enterOptions: "Enter options (comma separated)",
    moveUp: "Move Up",
    moveDown: "Move Down",
    removeField: "Remove Field",
    previewForm: "Preview Form",
    saveForm: "Save Form",
    formBuilder: "Form Builder",
    dragToReorder: "Drag to reorder fields",
  },

  invoices: {
    title: "Invoices",
    description: "Generate and manage repair invoices",
    generateInvoice: "Generate Invoice",
    invoiceList: "Invoice List",
    invoice: "Invoice",
    invoiceGenerator: "Invoice Generator",
    selectRepairs: "Select Repairs to Invoice",
    discountAmount: "Discount Amount (TND)",
    subtotal: "Subtotal",
    discount: "Discount",
    total: "Total",
  },
};

export default translations;
