// Remove the Translation import
const translations = {
  app: {
    title: "Fone2Go",
    slogan: "مرحبا بك في Fone2Go",
  },
  common: {
    optional: "اختياري",
    dashboard: "لوحة التحكم",
    newRepair: "إصلاح جديد",
    search: "بحث",
    stock: "المخزون ونقطة البيع",
    searchRepairs: "البحث عن الإصلاحات",
    viewDetails: "عرض التفاصيل",
    processing: "جاري المعالجة...",
    submit: "إرسال",
    update: "تحديث",
    delete: "حذف",
    cancel: "إلغاء",
    areYouSure: "هل أنت متأكد؟",
    deleteConfirmation: "هل أنت متأكد أنك تريد حذف هذا الإصلاح؟",
    back: "عودة إلى",
    loading: "تحميل...",
    print: "طباعة",
    download: "تحميل",
    balance: "الرصيد المتبقي",
    error: "خطأ!",
    pageNotFound: "عفواً! الصفحة غير موجودة",
    returnHome: "العودة إلى الصفحة الرئيسية",
    copied: "تم النسخ إلى الحافظة",
    call: "اتصال",
    startScanning: "بدأ المسح",
    stopScanning: "إيقاف المسح",
    scannerActive: "الماسح نشط وجاهز",
    scannerInactive: "الماسح غير نشط",
    logo: "الشعار",
    repairShop: "ورشة الإصلاح",
    tel: "هاتف",
    phone: "الهاتف",
    clear: "مسح",
    edit: "تعديل",
    actions: "الإجراءات",
    details: "التفاصيل",
    user: "المستخدم",
    date: "التاريخ",
    premium: "متقدم",
    viewDetails: "عرض التفاصيل",
    customerName: "اسم العميل",
    customerPhone: "هاتف العميل",
    notes: "ملاحظات",
    all: "الكل",
  },
  repair: {
    createNew: "إنشاء إصلاح جديد",
    customerInfo: "تفاصيل الزبون",
    customerName: "اسم الزبون",
    customerPhone: "رقم الهاتف  ",
    phoneModel: "موديل الجهاز",
    stat: "الحالة",
    problemDescription: "وصف المشكلة",
    observations: "ملاحظات",
    observationsOptional: "(اختياري)",
    addObservation: "إضافة ملاحظة",
    noObservations: "لا توجد ملاحظات حتى الآن",
    observationAdded: "تمت إضافة الملاحظة بنجاح",
    observationAddError: "فشل في إضافة الملاحظة",
    repairPrice: "سعر التصليح",
    initialPrice: "السعر الأولي",
    totalPrice: "السعر الإجمالي",
    paymentStatus: "حالة الدفع",
    downPayment: "دفعة أولى",
    priceModifications: "تعديلات السعر",
    priceModified: "تم تعديل السعر",
    addPriceModification: "إضافة تعديل السعر",
    priceModificationAdded: "تمت إضافة تعديل السعر بنجاح",
    priceModificationAddError: "فشل في إضافة تعديل السعر",
    amount: "المبلغ",
    reason: "السبب",
    submitRepair: "تسجيل طلب الإصلاح",
    successCreated: "تم إنشاء طلب الإصلاح بنجاح!",
    failedCreated: "فشل إنشاء طلب الإصلاح.",
    successUpdated: "تم تحديث طلب الإصلاح بنجاح!",
    notFound: "لم يتم العثور على طلب الإصلاح.",
    active: "نشط",
    completed: "مكتمل",
    returned: "مرتجع",
    all: "الكل",
    noActiveRepairs: "لا توجد إصلاحات نشطة",
    noCompletedRepairs: "لا توجد إصلاحات مكتملة",
    noReturnedRepairs: "لا توجد إصلاحات مرتجعة",
    noRepairsFound: "لا توجد إصلاحات",
    createFirstRepair: "إنشاء أول إصلاح لك",
    addedAgo: "أضيف منذ {{time}}",
    viewDetails: "عرض التفاصيل",
    scanQrCode: "امسح رمز الاستجابة السريعة",
    scanBarcode: "امسح الرمز الشريطي باستخدام الماسح الضوئي",
    barcodeSticker: "ملصق الرمز الشريطي",
    qrCode: "رمز الاستجابة السريعة",
    selectCodeType: "اختر نوع الرمز",
    selectCodeTypeDescription:
      "اختر نوع الرمز الذي سيتم تضمينه في التذكرة المطبوعة",
    repairDetails: "تفاصيل الإصلاح",
    repairTimeline: "الجدول الزمني للإصلاح",
    statusChanged: "تم تغيير الحالة إلى",
    updateStatus: "تحديث الحالة",
    deleteRepair: "حذف الإصلاح",
    deleteConfirmation: "هل أنت متأكد أنك تريد حذف هذا الإصلاح؟",
    enterDeleteCode: "أدخل رمز الحذف",
    invalidDeleteCode: "رمز الحذف غير صحيح. يرجى المحاولة مرة أخرى.",
    successDeleted: "تم حذف الإصلاح بنجاح!",
    thankYou: "شكرا لتعاملك معنا!",
    bringReceipt: "يرجى إحضار هذه الوثيقة عند استلام الجهاز.",
    repairTicket: "تذكرة إصلاح",
    ticketNumber: "رقم التذكرة",
    repairNumber: "رقم الإصلاح",
    printTicket: "طباعة التذكرة",
    createdAt: "تاريخ الإنشاء",
    status: {
      pending: "قيد الانتظار",
      inProgress: "قيد التنفيذ",
      completed: "مكتمل",
      returned: "مرتجع",
      cancelled: "ملغي",
    },
    analytics: {
      title: "تحليلات الدفع",
      totalAmountPaid: "المبلغ الكلي المدفوع",
      averageAmountPaid: "المبلغ المتوسط المدفوع",
      monthlyRevenue: "الدخل الشهري",
    },
    payment: {
      paid: "مدفوع",
      partial: "مدفوع جزئيا",
      unpaid: "غير مدفوع",
    },
    addError: "حدث خطأ أثناء إضافة الإصلاح.",
    fetchError: "حدث خطأ أثناء جلب الإصلاحات.",
    updateError: "حدث خطأ أثناء تحديث الإصلاح.",
    deleteError: "حدث خطأ أثناء حذف الإصلاح.",
    importError: "حدث خطأ أثناء استيراد الإصلاحات.",
    noRepairFoundWithTicket:
      "لم يتم العثور على إصلاح بهذا الرقم. يرجى التحقق والمحاولة مرة أخرى.",
    noTicketNumberForBarcode: "لا يوجد رقم تذكرة متاح لإنشاء الرمز الشريطي.",
  },
  searchRepairs: {
    title: "البحث عن الإصلاحات",
    noResults: "لم يتم العثور على إصلاحات",
    adjustFilters: "حاول تعديل البحث أو المرشحات",
  },
  filters: {
    title: "الفلاتر",
    slogan: "تصفية النتائج",
    status: "الحالة",
    payment: "الدفع",
    clear: "مسح التصفية",
    anyStatus: "أي حالة",
    anyPaymentStatus: "أي حالة دفع",
    clearAll: "مسح جميع المرشحات",
    dateFrom: "من تاريخ",
    dateTo: "إلى تاريخ",
  },
  importExport: {
    import: "استيراد",
    export: "تصدير",
    chooseFile: "اختر ملف",
    importRepairs: "استيراد الإصلاحات",
    exportRepairs: "تصدير الإصلاحات",
    downloadTemplate: "تنزيل النموذج",
    importedSuccess: "تم استيراد الإصلاحات بنجاح!",
    exportedSuccess: "تم تصدير الإصلاحات بنجاح!",
    invalidFile: "تنسيق ملف غير صالح. يرجى استخدام ملف JSON صالح.",
    importConfirmation:
      "هل أنت متأكد أنك تريد استيراد هذه الإصلاحات؟ سيؤدي ذلك إلى استبدال أي بيانات موجودة.",
    successImport: "تم استيراد الإصلاحات بنجاح!",
    failedImport: "فشل استيراد الإصلاحات.",
    successExport: "تم تصدير الإصلاحات بنجاح!",
    failedExport: "فشل تصدير الإصلاحات.",
  },
  analytics: {
    title: "تحليلات الدفع",
    totalAmountPaid: "المبلغ الكلي المدفوع",
    averageAmountPaid: "المبلغ المتوسط المدفوع",
    monthlyRevenue: "الدخل الشهري",
  },
  repairAnalytics: {
    title: "إحصائيات الإصلاحات",
    totalRepairs: "إجمالي عدد الإصلاحات",
    completedRepairs: "الإصلاحات المكتملة",
    pendingRepairs: "الإصلاحات المعلقة",
    totalRevenue: "إجمالي الإيرادات",
    averageRepairPrice: "متوسط سعر الإصلاح",
  },
  auth: {
    login: "تسجيل الدخول",
    logout: "تسجيل الخروج",
    email: "البريد الإلكتروني",
    password: "كلمة المرور",
    loginToContinue: "تسجيل الدخول للمتابعة إلى حسابك",
    genericError: "حدث خطأ أثناء تسجيل الدخول",
    emailRequired: "البريد الإلكتروني مطلوب",
    passwordRequired: "كلمة المرور مطلوبة",
    invalidCredentials: "بريد إلكتروني أو كلمة مرور غير صالحة",
  },
  languageSwitcher: {
    language: "اللغة",
    english: "الإنجليزية",
    french: "الفرنسية",
    arabic: "العربية",
  },
  data: {
    export: "تصدير البيانات",
    import: "استيراد البيانات",
    successExport: "تم تصدير البيانات بنجاح",
    failedExport: "فشل في تصدير البيانات",
    successImport: "تم استيراد البيانات بنجاح",
    failedImport: "فشل في استيراد البيانات",
  },
  repairShop: {
    title: "ورشة الإصلاح",
    name: "اسم الورشة",
    address: "العنوان",
    phone: "رقم الهاتف",
    fetchError: "فشل في جلب معلومات ورشة الإصلاح",
  },
  scanner: {
    henexScanner: "الماسح المخصص",
    cameraScanner: "ماسح الكاميرا",
    barcode: "الرمز الشريطي",
    henexInstructions: "الماسح المخصص نشط. قم بمسح رمز شريطي أو رمز QR مباشرة",
    scannerInstructions: "اضغط على زر الماسح لمسح الرمز",
    scanPlaceholder: "مسح رمز...",
    scanSuccess: "تم مسح الرمز بنجاح!",
    scanError: "خطأ في مسح الرمز. يرجى المحاولة مرة أخرى.",
    autoScanEnabled: "المسح التلقائي مفعل",
    readyToScan: "جاهز للمسح",
    scanningActive: "الماسح نشط",
  },
  shortcuts: {
    title: "اختصارات لوحة المفاتيح",
    description: "استخدم اختصارات لوحة المفاتيح هذه للتنقل بسرعة",
    keyboard: "اختصارات",
    showShortcuts: "عرض اختصارات لوحة المفاتيح",
    tip: "اضغط على ? في أي وقت لعرض مربع المساعدة هذا",
    goToDashboard: "الذهاب إلى لوحة التحكم (Alt+H)",
    newRepair: "إنشاء إصلاح جديد (Alt+N)",
    printCurrentRepair: "طباعة الإصلاح الحالي (Alt+P)",
    help: "عرض اختصارات لوحة المفاتيح (?)",
    navigatedToDashboard: "تم الانتقال إلى لوحة التحكم",
    creatingNewRepair: "إنشاء إصلاح جديد",
    printing: "جاري الطباعة...",
  },
  theme: {
    light: "فاتح",
    dark: "داكن",
    system: "النظام",
    toggleDarkMode: "تبديل الوضع المظلم",
  },
  dashboard: {
    title: "لوحة المعلومات",
    editLayout: "تعديل التخطيط",
    saveLayout: "حفظ التخطيط",
    reset: "إعادة تعيين",
    addWidget: "إضافة أداة",
    widgetAdded: "تمت إضافة الأداة بنجاح",
    widgetRemoved: "تمت إزالة الأداة",
    dashboardReset: "تمت إعادة تعيين لوحة المعلومات إلى الإعدادات الافتراضية",
    widgetType: "نوع الأداة",
    widgetTitle: "عنوان الأداة",
    selectWidgetType: "اختر نوع الأداة",
    addWidgetDescription: "اختر نوع الأداة وخصص عنوانها",
    totalRepairs: "إجمالي الإصلاحات",
    repairs: "إصلاحات",
    noRepairsFound: "لم يتم العثور على إصلاحات",
    unknownDevice: "جهاز غير معروف",
    goodMorning: "صباح الخير",
    goodAfternoon: "مساء الخير",
    goodEvening: "مساء الخير",
    user: "المستخدم",
    welcomeMessage:
      "مرحبًا بك في لوحة المعلومات المخصصة. هنا يمكنك رؤية نظرة عامة على ورشة الإصلاح الخاصة بك.",
    widgets: {
      welcome: {
        title: "مرحبًا",
        defaultTitle: "مرحبًا",
      },
      "repair-status": {
        title: "حالة الإصلاح",
        defaultTitle: "حالة الإصلاح",
      },
      "recent-repairs": {
        title: "الإصلاحات الأخيرة",
        defaultTitle: "الإصلاحات الأخيرة",
      },
      "repairs-by-status": {
        title: "الإصلاحات حسب الحالة",
        defaultTitle: "الإصلاحات حسب الحالة",
      },
      "repairs-by-device": {
        title: "الإصلاحات حسب الجهاز",
        defaultTitle: "الإصلاحات حسب الجهاز",
      },
      "payment-status": {
        title: "حالة الدفع",
        defaultTitle: "حالة الدفع",
      },
      income: {
        title: "الدخل",
        defaultTitle: "الدخل",
      },
    },
    paid: "مدفوع",
    unpaid: "غير مدفوع",
    partial: "مدفوع جزئيًا",
    noPaymentData: "لا توجد بيانات دفع متاحة",
    monthlyIncome: "الدخل الشهري",
    totalIncome: "إجمالي الدخل",
    income: "الدخل",
    increase: "زيادة",
    decrease: "انخفاض",
    allTime: "كل الوقت",
  },
  performanceMonitor: {
    title: "مراقب الأداء",
    subtitle: "مراقبة الأداء وتحليل المقاييس وتحسين العمليات التجارية",
    exportReport: "تصدير تقرير الأداء",
    processOverview: "نظرة عامة على العمليات",
    businessInsights: "رؤى الأعمال",
    improvementOpportunities: "فرص التحسين",
    analysisPeriod: "فترة التحليل",
    analyzingWorkflow: "تحليل أنماط الأداء لتحسين العمليات",
    workshopCapacity: "سعة الورشة",
    totalProductiveTime: "إجمالي الوقت الإنتاجي",
    processCompletion: "إنجاز العمليات",
    successfulOutcomes: "النتائج الناجحة",
    activeWorkflows: "سير العمل النشط",
    currentlyInProgress: "قيد التنفيذ حاليا",
    processEfficiency: "كفاءة العمليات",
    averageCycleTime: "متوسط وقت الدورة",
    overduePending: "معلق متأخر",
    pendingOverThreeDays: "معلق >3 أيام • انقر للعرض",
    businessPerformanceMetrics: "مقاييس الأداء التجاري",
    operationalExcellence: "المؤشرات الرئيسية للتميز التشغيلي ورضا العملاء",
    averageServiceTime: "متوسط وقت الخدمة",
    industryBenchmark: "معيار الصناعة: 45-90 دقيقة",
    processSuccessRate: "معدل نجاح العمليات",
    targetRate: "الهدف: >85%",
    throughput: "الإنتاجية (إصلاحات/ساعة)",
    workshopCapacityIndicator: "مؤشر سعة الورشة",
    strengthsIdentified: "نقاط القوة المحددة",
    optimizationOpportunities: "فرص التحسين",
    today: "اليوم",
    yesterday: "أمس",
    thisWeek: "هذا الأسبوع",
    thisMonth: "هذا الشهر",
  },
  workflowIntelligence: {
    title: "ذكاء سير العمل",
    subtitle: "تحسين العمليات وتحسين الكفاءة وتعزيز خدمة العملاء",
    processOverview: "نظرة عامة على العمليات",
    businessInsights: "رؤى الأعمال",
    improvementOpportunities: "فرص التحسين",
    analysisPeriod: "فترة التحليل",
    analyzingWorkflow: "تحليل أنماط سير العمل لتحسين العمليات",
    workshopCapacity: "سعة الورشة",
    totalProductiveTime: "إجمالي الوقت الإنتاجي",
    processCompletion: "إنجاز العمليات",
    successfulOutcomes: "النتائج الناجحة",
    activeWorkflows: "سير العمل النشط",
    currentlyInProgress: "قيد التنفيذ حاليا",
    processEfficiency: "كفاءة العمليات",
    averageCycleTime: "متوسط وقت الدورة",
    workshopResourceUtilization: "استخدام موارد الورشة",
    capacityDistribution: "فهم توزيع السعة عبر الفريق",
    processFlowAnalysis: "تحليل تدفق العمليات",
    workflowStageDistribution: "توزيع مراحل سير العمل وتحديد الاختناقات",
    businessPerformanceMetrics: "مقاييس الأداء التجاري",
    operationalExcellence: "المؤشرات الرئيسية للتميز التشغيلي ورضا العملاء",
    averageServiceTime: "متوسط وقت الخدمة",
    industryBenchmark: "معيار الصناعة: 45-90 دقيقة",
    processSuccessRate: "معدل نجاح العمليات",
    targetRate: "الهدف: >85%",
    throughput: "الإنتاجية (إصلاحات/ساعة)",
    workshopCapacityIndicator: "مؤشر سعة الورشة",
    processImprovementOpportunities: "فرص تحسين العمليات",
    dataDrivernInsights:
      "رؤى مدفوعة بالبيانات لتعزيز كفاءة الورشة ورضا العملاء",
    strengthsIdentified: "نقاط القوة المحددة",
    highCompletionRate: "معدل الإنجاز العالي يشير إلى تحكم جيد في العمليات",
    consistentServiceTimes: "أوقات الخدمة المتسقة تظهر إجراءات موحدة",
    optimizationAreas: "مجالات التحسين",
    workflowAutomation: "النظر في أتمتة سير العمل للمهام الروتينية",
    peakHourAnalysis: "تحليل ساعات الذروة لتخطيط أفضل للموارد",
    exportIntelligenceReport: "تصدير تقرير الذكاء",
    accessDenied: "تم رفض الوصول. تحتاج إلى امتيازات ذكاء سير العمل.",
    today: "اليوم",
    yesterday: "أمس",
    thisWeek: "هذا الأسبوع",
    thisMonth: "هذا الشهر",
    inProgress: "قيد التنفيذ",
    completed: "مكتمل",
    overduePending: "معلق متأخر",
    pendingOverThreeDays: "معلق >3 أيام • انقر للعرض",
    performanceSummary: "ملخص الأداء",
    successRate: "معدل النجاح",
    avgServiceTime: "متوسط وقت الخدمة",
    repairsPerHour: "إصلاحات/ساعة",
    overdue: "متأخر",
    excellentSuccessRate: "معدل نجاح ممتاز",
    successRateExceeds: "معدل الإنجاز {{rate}}% يتجاوز معايير الصناعة",
    efficientServiceTimes: "أوقات خدمة فعالة",
    avgTimeOptimal: "متوسط {{time}} دقيقة لكل إصلاح ضمن النطاق الأمثل",
    highProductivity: "إنتاجية عالية",
    completingRepairsPerHour:
      "إنجاز {{rate}} إصلاحات في الساعة يظهر كفاءة قوية",
    buildingFoundation: "بناء الأساس",
    focusOnOptimization: "التركيز على مجالات التحسين لتحسين مقاييس الأداء",
    optimizationOpportunities: "فرص التحسين",
    addressOverdueRepairs: "معالجة الإصلاحات المتأخرة",
    repairsPendingAttention:
      "{{count}} إصلاحات معلقة >3 أيام تحتاج اهتمام فوري",
    improveCompletionRate: "تحسين معدل الإنجاز",
    currentRateCanImprove: "المعدل الحالي {{rate}}% يمكن تحسينه إلى هدف 85%+",
    optimizeServiceTime: "تحسين وقت الخدمة",
    avgTimeExceedsBenchmark: "متوسط {{time}} دقيقة يتجاوز معيار 90 دقيقة",
    increaseThroughput: "زيادة الإنتاجية",
    currentThroughputImprove:
      "الإنتاجية الحالية {{rate}} إصلاحات/ساعة يمكن تحسينها",
    workflowStandardization: "توحيد سير العمل",
    createChecklistsReduce:
      "إنشاء قوائم مراجعة لأنواع الإصلاح الشائعة لتقليل التباين",
    recommendedActions: "الإجراءات الموصى بها",
    urgentProcessOverdue: "عاجل: معالجة الإصلاحات المتأخرة",
    reviewPrioritizeRepairs:
      "مراجعة وترتيب أولويات {{count}} إصلاحات معلقة أكثر من 3 أيام",
    teamTraining: "تدريب الفريق",
    identifySkillGaps: "تحديد فجوات المهارات وتوفير تدريب مستهدف للكفاءة",
    processDocumentation: "توثيق العمليات",
    documentBestPractices: "توثيق أفضل الممارسات من فترات الأداء العالي",
    capacityPlanning: "تخطيط السعة",
    useDataOptimizeStaffing:
      "استخدام البيانات لتحسين التوظيف خلال فترات الذروة",
  },
  stock: {
    title: "إدارة المخزون ونقطة البيع",
    pos: "نقطة البيع",
    products: "المنتجات",
    categories: "الفئات",
    sales: "المبيعات",
    inventory: "المخزون",
    reports: "التقارير",

    // Product Management
    addProduct: "إضافة منتج",
    editProduct: "تعديل منتج",
    deleteProduct: "حذف منتج",
    productName: "اسم المنتج",
    productDescription: "الوصف",
    sku: "رمز المنتج",
    barcode: "الباركود",
    category: "الفئة",
    price: "السعر",
    cost: "التكلفة",
    stock: "المخزون",
    minStock: "الحد الأدنى للمخزون",
    maxStock: "الحد الأقصى للمخزون",
    isActive: "نشط",
    image: "الصورة",

    // Categories
    addCategory: "إضافة فئة",
    editCategory: "تعديل فئة",
    categoryName: "اسم الفئة",
    categoryDescription: "وصف الفئة",

    // POS Interface
    cart: "السلة",
    addToCart: "إضافة إلى السلة",
    removeFromCart: "إزالة",
    clearCart: "إفراغ السلة",
    quantity: "الكمية",
    unitPrice: "سعر الوحدة",
    discount: "خصم",
    subtotal: "المجموع الفرعي",
    tax: "الضريبة",
    total: "المجموع",

    // Payment
    payment: "الدفع",
    paymentMethod: "طريقة الدفع",
    cash: "نقدي",
    card: "بطاقة",
    mobile: "دفع محمول",
    mixed: "دفع مختلط",
    amountPaid: "المبلغ المدفوع",
    change: "الباقي",
    processSale: "معالجة البيع",

    // Customer
    customer: "العميل",
    customerName: "اسم العميل",
    customerPhone: "هاتف العميل",

    // Sales
    saleNumber: "بيع #",
    saleDate: "تاريخ البيع",
    saleTotal: "إجمالي البيع",
    viewSale: "عرض البيع",

    // Inventory
    lowStock: "مخزون منخفض",
    outOfStock: "نفد من المخزون",
    stockLevel: "مستوى المخزون",
    stockMovement: "حركة المخزون",
    stockIn: "دخول مخزون",
    stockOut: "خروج مخزون",
    adjustment: "تعديل",

    // Messages
    productAdded: "تمت إضافة المنتج بنجاح",
    productUpdated: "تم تحديث المنتج بنجاح",
    productDeleted: "تم حذف المنتج بنجاح",
    categoryAdded: "تمت إضافة الفئة بنجاح",
    categoryUpdated: "تم تحديث الفئة بنجاح",
    categoryDeleted: "تم حذف الفئة بنجاح",
    saleCompleted: "تمت المبيعة بنجاح",
    insufficientStock: "مخزون غير كافي",
    invalidQuantity: "كمية غير صالحة",
    fetchError: "خطأ في جلب البيانات",
    addError: "خطأ في إضافة العنصر",
    updateError: "خطأ في تحديث العنصر",
    deleteError: "خطأ في حذف العنصر",
    saleError: "خطأ في معالجة البيع",
    stockAdjusted: "تم تعديل المخزون بنجاح",
    adjustError: "خطأ في تعديل المخزون",

    // Touch Interface
    touchMode: "وضع اللمس",
    gridView: "عرض الشبكة",
    listView: "عرض القائمة",
    searchProducts: "البحث عن المنتجات...",

    // Barcode Scanner
    scan: "مسح",
    scanBarcode: "مسح الرمز الشريطي",
    barcodeScanner: "ماسح الرمز الشريطي",
    manualEntry: "إدخال يدوي",
    cameraScanner: "ماسح الكاميرا",
    enterBarcode: "أدخل الرمز الشريطي",
    addToCart: "إضافة إلى السلة",
    productNotFound: "المنتج غير موجود",
    productInactive: "المنتج غير نشط",
    productOutOfStock: "المنتج نفد من المخزون",
    barcodeAdded: "تمت إضافة المنتج إلى السلة عبر الرمز الشريطي",
    scannerActive: "الماسح نشط - جاهز للمسح",
    scanning: "جاري المسح",
    scannerInstructions: "المسح التلقائي للرمز الشريطي نشط:",
    useBarcodeScannerDevice: "استخدم ماسح الرمز",
    typeBarcodeDirect: "اكتب الرمز مباشرة",
    automaticCartAdd: "إضافة تلقائية للسلة",

    // Sales Ticket & Printing
    salesTicket: "تذكرة البيع",
    printTicket: "طباعة التذكرة",
    print: "طباعة",
    saleNumber: "رقم البيع",
    date: "التاريخ",
    items: "العناصر",
    discount: "خصم",
    paid: "مدفوع",
    partial: "جزئي",
    pending: "معلق",
    thankYouVisit: "شكراً لزيارتكم!",
    keepTicket: "يرجى الاحتفاظ بهذه التذكرة",
    printInstructions1: "محسن للطابعات الحرارية 80 مم",
    printInstructions2: "تأكد من أن الطابعة متصلة وجاهزة",
    printInstructions3: "انقر طباعة لإنشاء الإيصال",

    // Sales History
    todaysSales: "مبيعات اليوم",
    todaysRevenue: "إيرادات اليوم",
    totalSales: "إجمالي المبيعات",
    noSalesYet: "لا توجد مبيعات بعد",
    walkInCustomer: "عميل عادي",
    saleId: "معرف البيع",
    totalValue: "القيمة الإجمالية",
    noLowStockItems: "لا توجد عناصر منخفضة المخزون",
    noOutOfStockItems: "لا توجد عناصر نفدت من المخزون",

    // Barcode Scanner
    cameraStarted: "تم تشغيل الكاميرا. وجه الكاميرا نحو الباركود للمسح.",
    cameraError: "لا يمكن الوصول إلى الكاميرا. يرجى استخدام الإدخال اليدوي.",
    manualEntry: "يدوي",
    cameraScanner: "كاميرا",
    enterBarcodeManually: "إدخال الباركود يدوياً",
    enterOrScanBarcode: "أدخل أو امسح الباركود...",
    pointCameraAtBarcode: "وجه الكاميرا نحو الباركود للمسح",
    orTypeBarcodeHere: "أو اكتب الباركود هنا...",
    cameraNotStarted: "لم يتم تشغيل الكاميرا",
    startCamera: "تشغيل الكاميرا",

    // Product Management
    showInactive: "إظهار غير النشط",
    hideInactive: "إخفاء غير النشط",
    inactive: "غير نشط",
    product: "المنتج",
    sku: "رمز المنتج",
    price: "السعر",
    stock: "المخزون",
    status: "الحالة",
    actions: "الإجراءات",
    tryAdjustingSearch: "حاول تعديل مصطلحات البحث",
    getStartedAddProduct: "ابدأ بإضافة منتجك الأول",

    // POS Interface Messages
    cartIsEmpty: "السلة فارغة",
    insufficientPayment: "مبلغ الدفع غير كافي",
    saleFailed: "فشلت العملية",
    unknownError: "خطأ غير معروف",
    saleProcessedSuccessfully: "تمت معالجة البيع بنجاح!",

    // Quick Setup
    userOrShopNotFound: "لم يتم العثور على المستخدم أو الورشة",
    errorCheckingSetup: "خطأ في فحص الإعداد",
    errorCreatingSampleData: "خطأ في إنشاء البيانات التجريبية",
    sampleDataCreatedSuccessfully: "تم إنشاء البيانات التجريبية بنجاح!",
    checking: "جاري الفحص...",
    checkSetup: "فحص الإعداد",
    createSampleData: "إنشاء بيانات تجريبية",

    // Product Form
    priceAndCostMustBePositive: "يجب أن يكون السعر والتكلفة أرقام موجبة",
    enterProductName: "أدخل اسم المنتج",
    enterSKU: "أدخل رمز المنتج",
    enterProductDescription: "أدخل وصف المنتج",
    enterBarcode: "أدخل الباركود",
    searchProducts: "البحث عن المنتجات...",

    // Stock Validation Messages
    productNoLongerExists: "المنتج لم يعد موجوداً",
    productNoLongerActive: "المنتج لم يعد نشطاً",
    insufficientStockDetailed:
      "مخزون غير كافي لـ {{productName}}. متوفر: {{available}}، مطلوب: {{required}}",

    // Advanced POS Features
    holdOrder: "تعليق الطلب",
    heldOrders: "الطلبات المعلقة",
    recallOrder: "استدعاء الطلب",
    deleteOrder: "حذف الطلب",
    orderHeld: "تم تعليق الطلب بنجاح",
    orderRecalled: "تم استدعاء الطلب بنجاح",
    orderDeleted: "تم حذف الطلب بنجاح",
    applyDiscount: "تطبيق خصم",
    orderDiscount: "خصم الطلب",
    itemDiscount: "خصم المنتج",
    discountType: "نوع الخصم",
    percentage: "نسبة مئوية",
    fixedAmount: "مبلغ ثابت",
    discountValue: "قيمة الخصم",
    discountApplied: "تم تطبيق الخصم بنجاح",
    notes: "ملاحظات",
    addNotes: "إضافة ملاحظات",
    orderNotes: "ملاحظات الطلب",
    noHeldOrders: "لا توجد طلبات معلقة",
    heldOn: "معلق في",
    orderTotal: "إجمالي الطلب",
    discountAmount: "مبلغ الخصم",
    cart: "السلة",
    customer: "العميل",
    customerName: "اسم العميل",
    customerPhone: "هاتف العميل",
    payment: "الدفع",
    subtotal: "المجموع الفرعي",
    tax: "الضريبة",
    total: "المجموع",
    paymentMethod: "طريقة الدفع",
    cash: "نقدي",
    card: "بطاقة",
    mobile: "محمول",
    amountPaid: "المبلغ المدفوع",
    change: "الباقي",
    processSale: "معالجة البيع",
    insufficientStock: "مخزون غير كافي",
  },

  // الميزات المتقدمة
  userManagement: {
    title: "إدارة المستخدمين",
    createUser: "إنشاء مستخدم",
    inviteUser: "دعوة مستخدم",
    email: "البريد الإلكتروني",
    password: "كلمة المرور",
    fullName: "الموظف",
    role: "الدور",
    administrator: "مدير",
    technician: "فني",
    receptionist: "موظف استقبال",
    cashier: "أمين صندوق",
    userCreated: "تم إنشاء المستخدم بنجاح",
    userUpdated: "تم تحديث المستخدم بنجاح",
    userDeleted: "تم حذف المستخدم بنجاح",
    removeUser: "إزالة المستخدم",
    changeRole: "تغيير الدور",
    activityLog: "سجل النشاط",
    noUsers: "لم يتم العثور على مستخدمين لهذه الورشة",
    noActivity: "لم يتم العثور على سجلات نشاط",
  },

  timeTracking: {
    title: "تتبع الوقت",
    overview: "نظرة عامة",
    entries: "إدخالات الوقت",
    reports: "التقارير",
    totalTime: "إجمالي الوقت",
    activeRepairs: "الإصلاحات النشطة",
    activeTechnicians: "الفنيين النشطين",
    completed: "مكتمل",
    activeNow: "نشط الآن",
    avgTime: "متوسط الوقت",
    perRepair: "لكل إصلاح",
    inProgress: "قيد التنفيذ",
    timeByTechnician: "الوقت حسب الفني",
    statusDistribution: "توزيع الحالة",
    technicianPerformance: "أداء الفنيين",
    timeDistribution: "توزيع الوقت",
    totalWorkTime: "إجمالي وقت العمل",
    completionRate: "معدل الإنجاز",
    avgRepairTime: "متوسط وقت الإصلاح",
    perCompletedRepair: "لكل إصلاح مكتمل",
    timeSpent: "الوقت المستغرق",
    repairs: "إصلاحات",
    entries: "إدخالات",
    repairsFinished: "إصلاحات منتهية",
    noTimeEntries: "لم يتم العثور على إدخالات وقت للمرشحات المحددة",
    noPerformanceData: "لا توجد بيانات أداء متاحة",
    noTimeData: "لا توجد بيانات وقت متاحة بعد",
    period: "الفترة",
    today: "اليوم",
    yesterday: "أمس",
    thisWeek: "هذا الأسبوع",
    thisMonth: "هذا الشهر",
    allTechnicians: "جميع الفنيين",
    editTimeEntry: "تعديل إدخال الوقت",
    duration: "المدة",
    durationMinutes: "المدة (بالدقائق)",
    manualEditNotAvailable:
      "التعديل اليدوي غير متاح - يتم حساب الوقت من تغييرات حالة الإصلاح",
    unknownUser: "مستخدم غير معروف",
    started: "بدأ",
    repair: "إصلاح",
    status: "الحالة",
    actions: "الإجراءات",
  },

  advancedStock: {
    title: "إدارة المخزون المتقدمة",
    inventory: "المخزون",
    lowStock: "مخزون منخفض",
    categories: "الفئات",
    movements: "حركات المخزون",
    addProduct: "إضافة منتج",
    addCategory: "إضافة فئة",
    recordMovement: "تسجيل حركة",
    productInventory: "مخزون المنتجات",
    lowStockAlerts: "تنبيهات المخزون المنخفض",
    productCategories: "فئات المنتجات",
    stockMovements: "حركات المخزون",
    noProducts: "لم يتم العثور على منتجات",
    noLowStock: "لم يتم العثور على منتجات بمخزون منخفض",
    noCategories: "لم يتم العثور على فئات",
    noMovements: "لم يتم العثور على حركات مخزون",
    restock: "إعادة تخزين",
    editCategory: "تعديل الفئة",
    editProduct: "تعديل المنتج",
    adjust: "تعديل",
    stockIn: "دخول مخزون",
    stockOut: "خروج مخزون",
    adjustment: "تعديل مباشر",
    reason: "السبب",
    reference: "المرجع",
    newQuantity: "الكمية الجديدة",
    quantityToAddRemove: "الكمية المراد إضافتها/إزالتها",
    whyChangeBeingMade: "لماذا يتم إجراء هذا التغيير؟",
    optionalReferenceNumber: "رقم مرجعي اختياري",
    stockAdded: "تمت إضافة المخزون بنجاح",
    stockRemoved: "تمت إزالة المخزون بنجاح",
    stockAdjusted: "تم تعديل المخزون بنجاح",
    notEnoughStock: "مخزون غير كافي متاح",
    trackAllStockChanges: "تتبع جميع تغييرات المخزون",
    dateTime: "التاريخ والوقت",
    product: "المنتج",
    type: "النوع",
    quantity: "الكمية",
    user: "المستخدم",
    usedInRepair: "مستخدم في الإصلاح",
  },

  assignTechnician: {
    assignTechnician: "تعيين فني",
    selectTechnician: "اختيار فني",
    chooseTechnician: "اختر فنياً",
    noAssignment: "لا يوجد تعيين",
    assignedTechnician: "الفني المعين",
    technicianAssignmentUpdated: "تم تحديث تعيين الفني",
    failedToUpdateTechnician: "فشل في تحديث تعيين الفني",
  },

  customForms: {
    title: "النماذج المخصصة",
    createForm: "إنشاء نموذج",
    editForm: "تعديل النموذج",
    deleteForm: "حذف النموذج",
    formName: "اسم النموذج",
    formDescription: "وصف النموذج",
    deviceType: "نوع الجهاز",
    fields: "الحقول",
    addField: "إضافة حقل",
    fieldName: "اسم الحقل",
    fieldType: "نوع الحقل",
    required: "مطلوب",
    optional: "اختياري",
    placeholder: "نص المساعدة",
    options: "الخيارات",
    fieldOrder: "ترتيب الحقل",
    textField: "حقل نص",
    numberField: "حقل رقم",
    textareaField: "منطقة نص",
    selectField: "قائمة منسدلة",
    checkboxField: "مربع اختيار",
    formCreated: "تم إنشاء النموذج بنجاح",
    formUpdated: "تم تحديث النموذج بنجاح",
    formDeleted: "تم حذف النموذج بنجاح",
    noForms: "لم يتم العثور على نماذج مخصصة",
    noFields: "لم يتم إضافة حقول بعد",
    selectCustomForm: "اختيار نموذج مخصص",
    noCustomForm: "لا يوجد نموذج مخصص",
    customFormCompleted: "تم إكمال النموذج المخصص",
    enterFieldName: "أدخل اسم الحقل",
    enterPlaceholder: "أدخل نص المساعدة",
    enterOptions: "أدخل الخيارات (مفصولة بفواصل)",
    moveUp: "نقل لأعلى",
    moveDown: "نقل لأسفل",
    removeField: "إزالة الحقل",
    previewForm: "معاينة النموذج",
    saveForm: "حفظ النموذج",
    formBuilder: "منشئ النماذج",
    dragToReorder: "اسحب لإعادة ترتيب الحقول",
  },

  invoices: {
    title: "الفواتير",
    description: "إنشاء وإدارة فواتير الإصلاح",
    generateInvoice: "إنشاء فاتورة",
    invoiceList: "قائمة الفواتير",
    invoice: "فاتورة",
    invoiceGenerator: "مولد الفواتير",
    selectRepairs: "اختيار الإصلاحات للفوترة",
    discountAmount: "مبلغ الخصم (دينار تونسي)",
    subtotal: "المجموع الفرعي",
    discount: "خصم",
    total: "المجموع",
  },
};

export default translations;
