// This file is automatically generated. Do not edit it directly.
import { createClient } from "@supabase/supabase-js";
import type { Database } from "./types";

const SUPABASE_URL = "https://qcidykrqgbdrnccxstrt.supabase.co";
const SUPABASE_PUBLISHABLE_KEY =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFjaWR5a3JxZ2Jkcm5jY3hzdHJ0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ1NTkzNzYsImV4cCI6MjA3MDEzNTM3Nn0.wJzTQz2M79JMZElg9ZdIM3nD1sD9OTqxDIuKj36IGOs";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(
  SUPABASE_URL,
  SUPABASE_PUBLISHABLE_KEY
);
