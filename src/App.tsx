import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { HashRouter, Routes, Route, Navigate } from "react-router-dom";
import { RepairProvider } from "./context/RepairContext";
import { AuthProvider } from "./context/AuthContext";
import { RepairShopProvider } from "./context/RepairShopContext";
import { StockProvider } from "./context/StockContext";
import { ThemeProvider } from "./context/ThemeContext";
import Layout from "./components/Layout";
import Dashboard from "./pages/Dashboard";
import NewRepair from "./pages/NewRepair";
import RepairDetail from "./pages/RepairDetail";
import SearchPage from "./pages/SearchPage";
import NotFound from "./pages/NotFound";
import StockManagement from "./pages/StockManagement";
import UserManagement from "./pages/UserManagement";
import Login from "./pages/Login";
import TechnicianTimeTracking from "./components/TechnicianTimeTracking";
import AdvancedStockManagement from "./components/AdvancedStockManagement";
import CustomizableForms from "./components/CustomizableForms";
import Invoices from "./pages/Invoices";
import AdminTest from "./pages/AdminTest";
import ProtectedRoute from "./components/ProtectedRoute";
// Import i18n to ensure it's initialized
import "./i18n";

// Create a client
const queryClient = new QueryClient();

const App = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <HashRouter>
        <AuthProvider>
          <ThemeProvider>
            <RepairShopProvider>
              <RepairProvider>
                <StockProvider>
                  <TooltipProvider>
                    <Routes>
                      <Route path="login" element={<Login />} />
                      <Route element={<ProtectedRoute />}>
                        <Route path="/" element={<Layout />}>
                          <Route index element={<Dashboard />} />
                          <Route path="new-repair" element={<NewRepair />} />
                          <Route path="repair/:id" element={<RepairDetail />} />
                          <Route path="search" element={<SearchPage />} />
                          <Route path="stock" element={<StockManagement />} />
                          <Route path="invoices" element={<Invoices />} />
                          <Route path="users" element={<UserManagement />} />
                          <Route
                            path="time-tracking"
                            element={<TechnicianTimeTracking />}
                          />
                          <Route
                            path="advanced-stock"
                            element={<AdvancedStockManagement />}
                          />
                          <Route
                            path="custom-forms"
                            element={<CustomizableForms />}
                          />
                          <Route path="admin-test" element={<AdminTest />} />
                          <Route path="*" element={<NotFound />} />
                        </Route>
                      </Route>
                    </Routes>
                    <Toaster />
                    <Sonner />
                  </TooltipProvider>
                </StockProvider>
              </RepairProvider>
            </RepairShopProvider>
          </ThemeProvider>
        </AuthProvider>
      </HashRouter>
    </QueryClientProvider>
  );
};

export default App;
