#!/bin/bash

# This script checks if the CSP header is being applied correctly

echo "=== Checking CSP Header ==="
echo "This script will check if the CSP header is being applied correctly"

# Domain to check
DOMAIN="fone2go.talifouni.com"

# Check if curl is installed
if ! command -v curl &> /dev/null; then
    echo "Error: curl is not installed. Please install it and try again."
    exit 1
fi

# Check the CSP header
echo "Checking CSP header for $DOMAIN..."
CSP_HEADER=$(curl -s -I "https://$DOMAIN" | grep -i "Content-Security-Policy")

if [ -z "$CSP_HEADER" ]; then
    echo "No CSP header found. This could indicate a problem with your Nginx configuration."
    echo "Checking for other security headers..."
    
    # Check for other security headers to see if headers are being applied at all
    OTHER_HEADERS=$(curl -s -I "https://$DOMAIN" | grep -i -E "X-Content-Type-Options|X-Frame-Options|X-XSS-Protection")
    
    if [ -z "$OTHER_HEADERS" ]; then
        echo "No security headers found. This suggests that your Nginx configuration is not applying headers correctly."
    else
        echo "Other security headers found:"
        echo "$OTHER_HEADERS"
        echo "This suggests that your Nginx configuration is applying headers, but the CSP header is missing."
    fi
else
    echo "CSP header found:"
    echo "$CSP_HEADER"
    
    # Check if the CSP header includes WebSocket connections
    if echo "$CSP_HEADER" | grep -q "wss://"; then
        echo "WebSocket connections are allowed in the CSP header."
        
        # Check specifically for Supabase WebSocket connections
        if echo "$CSP_HEADER" | grep -q "wss://.*supabase.co"; then
            echo "Supabase WebSocket connections are allowed in the CSP header."
        else
            echo "Supabase WebSocket connections are NOT explicitly allowed in the CSP header."
        fi
    else
        echo "WebSocket connections are NOT allowed in the CSP header."
    fi
fi

echo ""
echo "=== Check Completed ==="
echo "If the CSP header is not being applied correctly, run the direct-csp-fix.sh script to fix it."
echo "After fixing, visit https://$DOMAIN/test-csp to test the CSP settings."
