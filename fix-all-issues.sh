#!/bin/bash

# This script fixes all issues by updating both the Docker container and Nginx configuration

echo "=== Fixing All Issues ==="
echo "This script will update both your Docker container and Nginx configuration"

# Step 1: Rebuild and restart the Docker container
echo "Step 1: Rebuilding and restarting the Docker container..."

# Stop the current Docker container
echo "Stopping current Docker container..."
docker stop repair-qr-ninja || true

# Remove the container
echo "Removing container..."
docker rm repair-qr-ninja || true

# Rebuild the Docker image
echo "Rebuilding Docker image..."
docker build -t repair-qr-ninja .

# Start the container with the updated configuration
echo "Starting container with updated configuration..."
docker run -d --name repair-qr-ninja -p 3001:3000 \
  -e NODE_ENV=production \
  -e VITE_SUPABASE_URL=${SUPABASE_URL:-https://qvwbetihlprjphvyaawi.supabase.co} \
  -e VITE_SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY:-eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF2d2JldGlobHByanBodnlhYXdpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ5NjQ0NzUsImV4cCI6MjA2MDU0MDQ3NX0.tjWg4fK22p50S2pIU898_BrsEL0rcc6p7HhK1Bzo4_U} \
  -v $(pwd)/public:/app/public \
  repair-qr-ninja

# Step 2: Update Nginx configuration
echo "Step 2: Updating Nginx configuration..."
echo "Please enter your sudo password when prompted."

# Assuming the Nginx config is in /etc/nginx/sites-available/
NGINX_CONFIG_PATH="/etc/nginx/sites-available/fone2go.talifouni.com"

# Check if the file exists
if [ -f "$NGINX_CONFIG_PATH" ]; then
    # Backup the existing config
    sudo cp "$NGINX_CONFIG_PATH" "${NGINX_CONFIG_PATH}.bak.$(date +%Y%m%d%H%M%S)"
    
    # Copy the new config
    sudo cp nginx-host-config-fixed.conf "$NGINX_CONFIG_PATH"
    
    # Test Nginx configuration
    echo "Testing Nginx configuration..."
    sudo nginx -t
    
    if [ $? -eq 0 ]; then
        # Reload Nginx if the test was successful
        echo "Reloading Nginx..."
        sudo systemctl reload nginx
        echo "Nginx configuration updated and reloaded successfully."
    else
        echo "Nginx configuration test failed. Reverting to backup..."
        sudo cp "${NGINX_CONFIG_PATH}.bak.$(ls -t ${NGINX_CONFIG_PATH}.bak.* | head -1)" "$NGINX_CONFIG_PATH"
        sudo systemctl reload nginx
        echo "Reverted to previous configuration."
        exit 1
    fi
else
    echo "Nginx configuration file not found at $NGINX_CONFIG_PATH"
    echo "Please manually update your Nginx configuration with the content from nginx-host-config-fixed.conf"
    echo "Then reload Nginx with: sudo systemctl reload nginx"
fi

echo ""
echo "=== Fix Completed ==="
echo "Both the Docker container and Nginx configuration have been updated."
echo "Please check your application at https://fone2go.talifouni.com to verify the issues are resolved."
echo ""
echo "If you still encounter issues, you may need to:"
echo "1. Clear your browser cache or use incognito mode"
echo "2. Check the Docker container logs: docker logs repair-qr-ninja"
echo "3. Check the Nginx error logs: sudo tail -f /var/log/nginx/error.log"
