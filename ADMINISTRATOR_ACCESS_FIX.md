# Administrator Access Fix

## Problem Description

Administrators are unable to see all repairs in their repair shop. This issue occurs because:

1. **Overly Restrictive RLS Policies**: The current Row Level Security (RLS) policies only allow users to see repairs they created or are assigned to, rather than all repairs in their repair shop.

2. **Role Assignment Issues**: New administrator accounts may not have the correct role assigned in the `user_repair_shops` table.

## Root Cause

The issue was introduced by the `fix_rls_policies.sql` migration which simplified the RLS policies too much. The current policy:

```sql
CREATE POLICY "Users can view their own repairs" ON public.repairs
    FOR SELECT USING (
        user_id = auth.uid() OR assigned_technician = auth.uid()
    );
```

This policy only allows users to see repairs they created or are assigned to, but administrators should see ALL repairs in their repair shop.

## Solution

### Step 1: Apply the Database Migration

Run the following SQL migration in your Supabase SQL Editor:

**File: `migrations/fix_administrator_repair_access.sql`**

```sql
/*
 * REPAIR QR NINJA - FIX ADMINISTRATOR REPAIR ACCESS
 */

-- Start transaction
BEGIN;

-- Drop the overly restrictive policy
DROP POLICY IF EXISTS "Users can view their own repairs" ON public.repairs;
DROP POLICY IF EXISTS "Users can insert repairs" ON public.repairs;
DROP POLICY IF EXISTS "Users can update their own repairs" ON public.repairs;
DROP POLICY IF EXISTS "Users can delete their own repairs" ON public.repairs;

-- Create proper role-based repair policies
CREATE POLICY "Users can view repairs from their repair shops" ON public.repairs
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_repair_shops.repair_shop_id = repairs.repair_shop_id
            AND user_repair_shops.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert repairs for their repair shops" ON public.repairs
    FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_repair_shops.repair_shop_id = repairs.repair_shop_id
            AND user_repair_shops.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update repairs from their repair shops" ON public.repairs
    FOR UPDATE
    USING (
        EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_repair_shops.repair_shop_id = repairs.repair_shop_id
            AND user_repair_shops.user_id = auth.uid()
            AND (
                user_repair_shops.role = 'administrator' OR
                repairs.user_id = auth.uid() OR
                repairs.assigned_technician = auth.uid()
            )
        )
    );

CREATE POLICY "Users can delete repairs from their repair shops" ON public.repairs
    FOR DELETE
    USING (
        EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_repair_shops.repair_shop_id = repairs.repair_shop_id
            AND user_repair_shops.user_id = auth.uid()
            AND (
                user_repair_shops.role = 'administrator' OR
                repairs.user_id = auth.uid()
            )
        )
    );

COMMIT;
```

### Step 2: Verify and Fix Role Assignments

Use the diagnostic script to check user roles:

**File: `migrations/verify_and_fix_admin_roles.sql`**

1. **Check all users and their roles:**
```sql
SELECT 
    u.email,
    u.id as user_id,
    rs.name as repair_shop_name,
    urs.role,
    urs.created_at as role_assigned_at
FROM auth.users u
LEFT JOIN public.user_repair_shops urs ON u.id = urs.user_id
LEFT JOIN public.repair_shops rs ON urs.repair_shop_id = rs.id
ORDER BY u.email, rs.name;
```

2. **Assign administrator role to a user:**
```sql
SELECT assign_admin_role('<EMAIL>');
```

### Step 3: Test the Fix

1. **Add the test component** to your application (temporarily):

Add this to your NavBar or Dashboard:
```tsx
import AdminAccessTest from "@/components/AdminAccessTest";

// In your component:
<AdminAccessTest />
```

2. **Expected Results for Administrators:**
   - User Role Check: Should show "administrator"
   - Repair Access Test: Should show ALL repairs in the shop
   - User Shop Association Test: Should show repair shop membership
   - RLS Policy Test: Should work correctly

## How to Apply the Fix

### Option 1: Supabase Dashboard (Recommended)

1. Go to your Supabase project dashboard
2. Navigate to the SQL Editor
3. Copy and paste the content of `migrations/fix_administrator_repair_access.sql`
4. Run the query
5. If needed, run the role assignment queries from `migrations/verify_and_fix_admin_roles.sql`

### Option 2: Command Line (if you have psql installed)

```bash
psql "$DATABASE_URL" -f migrations/fix_administrator_repair_access.sql
```

## Verification Steps

1. **Login as an administrator**
2. **Check the repairs list** - you should now see ALL repairs in your repair shop
3. **Test the technician toggle** - it should still work for technicians
4. **Create a new admin account** and verify it can see all repairs

## Technical Details

### What Changed

1. **RLS Policy**: Changed from user-specific access to shop-based access
2. **Administrator Privileges**: Administrators can now see all repairs in their shop
3. **Security**: Maintained - users can only access repairs from shops they belong to
4. **Technician Toggle**: Still works as expected in the application layer

### Application-Level Filtering

The `RepairContext.tsx` still handles technician-specific filtering:

```tsx
// Filter by assigned technician if user is technician and toggle is off
if (!showAllTickets) {
  const { data: userShopData } = await supabase
    .from("user_repair_shops")
    .select("role")
    .eq("user_id", user.id)
    .single();
  
  if (userShopData?.role === "technician") {
    query = query.eq("assigned_technician", user.id);
  }
}
```

This ensures that:
- **Administrators**: Always see all repairs (no filtering applied)
- **Technicians**: See only assigned repairs when toggle is OFF, all repairs when toggle is ON
- **Other roles**: See all repairs in their shop

## Troubleshooting

### Issue: New admin account shows 0 repairs

**Solution**: Check role assignment
```sql
-- Check if user has a role
SELECT * FROM public.user_repair_shops WHERE user_id = 'user-uuid-here';

-- Assign administrator role
SELECT assign_admin_role('<EMAIL>');
```

### Issue: Still can't see all repairs after migration

**Solution**: Verify the migration was applied
```sql
-- Check current policies
SELECT schemaname, tablename, policyname, cmd, qual 
FROM pg_policies 
WHERE tablename = 'repairs';
```

### Issue: Application errors after migration

**Solution**: Clear browser cache and refresh the application. The RLS policy changes should be immediately effective.

## Support

If you continue to experience issues:

1. Run the `AdminAccessTest` component to get detailed diagnostics
2. Check the browser console for any JavaScript errors
3. Verify the Supabase connection is working
4. Ensure the user is properly authenticated

The fix addresses the core RLS policy issue while maintaining security and the existing application functionality.
